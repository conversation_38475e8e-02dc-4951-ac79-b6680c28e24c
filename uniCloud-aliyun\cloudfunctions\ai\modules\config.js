/**
 * AI 配置和常量管理模块
 *
 * 主要功能：
 * 1. 管理豆包 AI 的连接配置参数
 * 2. 维护工具注册表，定义所有可用的云函数工具
 * 3. 定义 SSE 消息类型常量，统一前后端通信协议
 * 4. 提供默认系统提示词，指导 AI 进行意图识别
 *
 * 设计原则：
 * - 集中化配置管理，便于维护和修改
 * - 类型安全的参数定义，减少运行时错误
 * - 可扩展的工具注册机制，支持动态添加新工具
 * - 标准化的消息类型定义，确保通信协议一致性
 *
 * 使用场景：
 * - 初始化 AI 客户端时获取连接参数
 * - 执行计划生成时查询可用工具
 * - SSE 推送时使用标准消息类型
 * - 意图识别时使用默认系统提示词
 *
 * <AUTHOR> 开发团队
 * @version 1.3.0
 * @since 2024-01-01
 */

const doubaoParams = {
  baseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  apiKey: 'a1d08626-e9fa-425b-a8cf-e59f274d3a3d', // API 密钥
  timeout: 30000, // 请求超时时间（毫秒），30 秒超时
}

/**
 * Function Calling 工具定义
 * 基于豆包模型原生 Function Calling 标准格式
 * 根据技术文档规范定义的工具集合
 */
const FUNCTION_TOOLS = [
  {
    type: 'function',
    function: {
      name: 'getTasks',
      description: '获取任务列表，支持按项目、状态、关键词筛选',
      parameters: {
        type: 'object',
        properties: {
          projectName: {
            type: 'string',
            description: '项目名称，可选',
          },
          completed: {
            type: 'boolean',
            description: '任务完成状态，true=已完成，false=未完成，不传=全部',
          },
          keyword: {
            type: 'string',
            description: '搜索关键词，匹配任务标题和内容',
          },
          priority: {
            type: 'integer',
            description: '优先级筛选：0=无，1=低，3=中，5=高',
          },
          limit: {
            type: 'integer',
            description: '返回数量限制，默认 20',
            minimum: 1,
            maximum: 100,
          },
        },
      },
    },
  },
  {
    type: 'function',
    function: {
      name: 'createTask',
      description: '创建新任务',
      parameters: {
        type: 'object',
        properties: {
          title: {
            type: 'string',
            description: '任务标题',
          },
          content: {
            type: 'string',
            description: '任务详细内容，可选',
          },
          projectName: {
            type: 'string',
            description: '所属项目名称，可选',
          },
          priority: {
            type: 'integer',
            description: '优先级：0=无，1=低，3=中，5=高',
            enum: [0, 1, 3, 5],
          },
          dueDate: {
            type: 'string',
            description: '截止日期，格式：YYYY-MM-DD HH:MM:SS',
          },
        },
        required: ['title'],
      },
    },
  },
  {
    type: 'function',
    function: {
      name: 'getProjects',
      description: '获取项目列表',
      parameters: {
        type: 'object',
        properties: {
          keyword: {
            type: 'string',
            description: '项目名称搜索关键词',
          },
          includeClosed: {
            type: 'boolean',
            description: '是否包含已关闭项目，默认 false',
          },
        },
      },
    },
  },
  {
    type: 'function',
    function: {
      name: 'updateTask',
      description: '更新任务信息',
      parameters: {
        type: 'object',
        properties: {
          taskId: {
            type: 'string',
            description: '任务 ID',
          },
          title: {
            type: 'string',
            description: '新的任务标题',
          },
          content: {
            type: 'string',
            description: '新的任务内容',
          },
          completed: {
            type: 'boolean',
            description: '任务完成状态',
          },
          priority: {
            type: 'integer',
            description: '优先级：0=无，1=低，3=中，5=高',
            enum: [0, 1, 3, 5],
          },
          projectName: {
            type: 'string',
            description: '新的项目名称',
          },
        },
        required: ['taskId'],
      },
    },
  },
]

/**
 * Function Calling 专用 SSE 消息类型
 * 基于技术文档规范定义的简化消息类型
 */
const SSE_MESSAGE_TYPES = {
  // === 基础流程消息 ===
  PROCESSING_START: 'processing_start', // 开始处理用户请求
  SESSION_END: 'session_end', // 会话结束
  ERROR: 'error', // 系统错误

  // === 聊天内容消息 ===
  CHAT_CONTENT_CHUNK: 'chat_content_chunk', // 流式聊天内容块

  // === Function Calling 专用消息 ===
  TOOL_CALL_START: 'tool_call_start', // 工具调用开始
  TOOL_EXECUTION_START: 'tool_execution_start', // 工具执行开始
  TOOL_EXECUTION_COMPLETE: 'tool_execution_complete', // 工具执行完成
  TOOL_EXECUTION_ERROR: 'tool_execution_error', // 工具执行失败
  TOOL_RESULT_PROCESSING: 'tool_result_processing', // 工具结果处理中
  TOOL_RESULT_ERROR: 'tool_result_error', // 工具结果处理失败
}

/**
 * SSE消息构建工具函数
 * 用于创建标准化的SSE消息格式
 */
const createSSEMessage = (type, sessionId, data = null) => {
  return {
    type,
    timestamp: Date.now(),
    sessionId,
    data,
  }
}

/**
 * 生成会话ID
 */
const generateSessionId = () => {
  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

module.exports = {
  FUNCTION_TOOLS,
  SSE_MESSAGE_TYPES,
  doubaoParams,
  createSSEMessage,
  generateSessionId,
}
